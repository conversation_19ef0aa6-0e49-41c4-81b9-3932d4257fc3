my.appio.so {
        root * /var/www/my.appio.so/fe/dist

        # to servve .html files without extension
        try_files {path} {path}.html

        file_server
        encode zstd gzip
        header -Server # Remove the Server header

        handle_errors {
                rewrite * /404.html
                file_server
        }

        # tls /etc/caddy/certs/cloudflare.crt /etc/caddy/certs/cloudflare.key
        log {
                output file /var/log/caddy/my.appio.so-access.log
        }
        log {
                level ERROR
                output file /var/log/caddy/my.appio.so-error.log
        }
}
