import { useAuth } from '../../hooks/useAuth'

export const Header = () => {
  const { user, signOut } = useAuth()

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          <h1 className="logo">
            <a href="/">Appio</a>
          </h1>
          
          {user && (
            <nav className="nav">
              <span className="user-email">{user.email}</span>
              <button 
                onClick={handleSignOut}
                className="btn btn-outline"
              >
                Sign Out
              </button>
            </nav>
          )}
        </div>
      </div>
    </header>
  )
}
