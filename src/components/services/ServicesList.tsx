import { useState, useEffect } from 'react'
import { apiService } from '../../services/api'
import type { Service, ApiError } from '../../types'

export const ServicesList = () => {
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await apiService.getServices()
        setServices(data)
      } catch (err: any) {
        const apiError = err as ApiError
        setError(apiError.message || 'Failed to fetch services')
      } finally {
        setLoading(false)
      }
    }

    fetchServices()
  }, [])

  if (loading) {
    return (
      <div className="services-loading">
        <p>Loading services...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="services-error">
        <div className="alert alert-error">
          {error}
        </div>
        <button 
          onClick={() => window.location.reload()}
          className="btn btn-outline"
        >
          Retry
        </button>
      </div>
    )
  }

  if (services.length === 0) {
    return (
      <div className="services-empty">
        <h3>No Services Found</h3>
        <p>You don't have any services yet. Create your first service to get started.</p>
      </div>
    )
  }

  return (
    <div className="services-list">
      <h2>Your Services</h2>
      <div className="services-grid">
        {services.map((service) => (
          <div key={service.id} className="service-card">
            {service.logo_url && (
              <div className="service-logo">
                <img src={service.logo_url} alt={`${service.title} logo`} />
              </div>
            )}
            
            <div className="service-content">
              <h3 className="service-title">{service.title}</h3>
              <p className="service-description">{service.description}</p>
              
              {service.url && (
                <a 
                  href={service.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="service-link"
                >
                  Visit Service
                </a>
              )}
            </div>
            
            <div className="service-actions">
              <button className="btn btn-outline btn-sm">
                Edit
              </button>
              <button className="btn btn-primary btn-sm">
                Manage
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
