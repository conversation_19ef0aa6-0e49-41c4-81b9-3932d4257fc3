import { useAuth } from '../../hooks/useAuth'

export const LoginForm = () => {
  const { signIn, signUp } = useAuth()

  const handleSignIn = () => {
    signIn()
  }

  const handleSignUp = () => {
    signUp()
  }

  return (
    <div className="auth-form">
      <h2>Welcome to Appio</h2>
      <p>Please sign in to access your dashboard</p>

      <div className="auth-buttons">
        <button
          onClick={handleSignIn}
          className="btn btn-primary btn-full"
        >
          Sign In
        </button>

        <button
          onClick={handleSignUp}
          className="btn btn-outline btn-full"
        >
          Create Account
        </button>
      </div>
    </div>
  )
}
