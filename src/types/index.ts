export interface User {
  id: string;
  email: string;
  created_at: string;
}

export interface Service {
  id: string;
  title: string;
  description: string;
  logo_url?: string;
  banner_url?: string;
  url?: string;
}

export interface AuthFormData {
  email: string;
  password: string;
}

export interface SignupFormData extends AuthFormData {
  confirmPassword: string;
}

export interface ApiError {
  message: string;
  status?: number;
}
