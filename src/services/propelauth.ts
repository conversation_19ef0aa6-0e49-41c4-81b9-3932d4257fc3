import { AuthProvider } from '@propelauth/react'

const propelAuthUrl = import.meta.env.VITE_PROPELAUTH_URL

console.log('Environment variables:', import.meta.env)
console.log('PropelAuth URL:', propelAuthUrl)

if (!propelAuthUrl) {
  throw new Error(`Missing VITE_PROPELAUTH_URL environment variable. Available env vars: ${Object.keys(import.meta.env).join(', ')}`)
}

export { AuthProvider }
export const authUrl = propelAuthUrl
