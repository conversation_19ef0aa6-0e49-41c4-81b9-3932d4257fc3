import { supabase } from './supabase'
import type { Service, ApiError } from '../types'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://api.appio.so'

class ApiService {
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session?.access_token) {
      throw new Error('No authentication token available')
    }

    return {
      'Authorization': `Bearer ${session.access_token}`,
      'Content-Type': 'application/json',
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const error: ApiError = {
        message: `HTTP ${response.status}: ${response.statusText}`,
        status: response.status,
      }
      
      try {
        const errorData = await response.json()
        error.message = errorData.message || error.message
      } catch {
        // Use default error message if JSON parsing fails
      }
      
      throw error
    }

    return response.json()
  }

  async getServices(): Promise<Service[]> {
    const headers = await this.getAuthHeaders()
    
    const response = await fetch(`${API_BASE_URL}/v1/services`, {
      method: 'GET',
      headers,
    })

    return this.handleResponse<Service[]>(response)
  }

  async getService(id: string): Promise<Service> {
    const headers = await this.getAuthHeaders()
    
    const response = await fetch(`${API_BASE_URL}/v1/services/${id}`, {
      method: 'GET',
      headers,
    })

    return this.handleResponse<Service>(response)
  }
}

export const apiService = new ApiService()
