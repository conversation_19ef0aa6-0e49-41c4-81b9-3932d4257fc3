<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appio - Dashboard</title>
    <link rel="stylesheet" href="https://cdn.appio.so/css/appio.css">
    <style>
      /* Custom styles for auth pages */
      .auth-page {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
      }

      .auth-container {
        max-width: 400px;
        width: 100%;
        padding: 2rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .auth-form h2 {
        text-align: center;
        margin-bottom: 1.5rem;
        color: #333;
      }

      .auth-link {
        text-align: center;
        margin-top: 1rem;
      }

      .auth-link a {
        color: #007bff;
        text-decoration: none;
      }

      .auth-link a:hover {
        text-decoration: underline;
      }

      /* Dashboard styles */
      .dashboard-header {
        margin-bottom: 2rem;
      }

      .dashboard-header h1 {
        margin-bottom: 0.5rem;
      }

      .dashboard-header p {
        color: #666;
        margin: 0;
      }

      /* Services styles */
      .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
      }

      .service-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .service-logo {
        text-align: center;
        margin-bottom: 1rem;
      }

      .service-logo img {
        width: 64px;
        height: 64px;
        border-radius: 8px;
        object-fit: cover;
      }

      .service-title {
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
        font-weight: 600;
      }

      .service-description {
        color: #666;
        margin: 0 0 1rem 0;
        line-height: 1.5;
      }

      .service-link {
        display: inline-block;
        color: #007bff;
        text-decoration: none;
        margin-bottom: 1rem;
      }

      .service-link:hover {
        text-decoration: underline;
      }

      .service-actions {
        display: flex;
        gap: 0.5rem;
      }

      .services-loading,
      .services-error,
      .services-empty {
        text-align: center;
        padding: 2rem;
      }

      /* Header styles */
      .header {
        background: white;
        border-bottom: 1px solid #e0e0e0;
        padding: 1rem 0;
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo a {
        text-decoration: none;
        color: #333;
        font-size: 1.5rem;
        font-weight: bold;
      }

      .nav {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .user-email {
        color: #666;
        font-size: 0.9rem;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/main.tsx"></script>
  </body>
</html>