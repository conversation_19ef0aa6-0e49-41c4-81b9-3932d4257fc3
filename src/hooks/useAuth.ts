import { useAuthInfo, useLogoutFunction, useRedirectFunctions } from '@propelauth/react'
import { useEffect } from 'react'
import { apiService } from '../services/api'
import type { User } from '../types'

export const useAuth = () => {
  const authInfo = useAuthInfo()
  const logoutFn = useLogoutFunction()
  const { redirectToLoginPage, redirectToSignupPage } = useRedirectFunctions()

  // Update API service with access token when auth state changes
  useEffect(() => {
    if (authInfo.accessToken) {
      apiService.setAccessToken(authInfo.accessToken)
    } else {
      apiService.setAccessToken(null)
    }
  }, [authInfo.accessToken])

  const user: User | null = authInfo.user ? {
    id: authInfo.user.userId,
    email: authInfo.user.email || '',
    created_at: '', // PropelAuth doesn't provide this directly
  } : null

  const signUp = async () => {
    redirectToSignupPage()
  }

  const signIn = async () => {
    redirectToLoginPage()
  }

  const signOut = async () => {
    logoutFn(true) // true = redirect to login page after logout
  }

  return {
    user,
    loading: authInfo.loading,
    signUp,
    signIn,
    signOut,
    isLoggedIn: !!authInfo.user,
  }
}
