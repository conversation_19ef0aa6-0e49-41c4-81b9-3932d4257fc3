import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Layout } from '../components/layout/Layout'
import { SignupForm } from '../components/auth/SignupForm'
import { useAuth } from '../hooks/useAuth'

export const SignupPage = () => {
  const { user, loading } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!loading && user) {
      navigate('/dashboard')
    }
  }, [user, loading, navigate])

  if (loading) {
    return (
      <Layout showHeader={false}>
        <div className="auth-page">
          <div className="container">
            <p>Loading...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout showHeader={false}>
      <div className="auth-page">
        <div className="container">
          <div className="auth-container">
            <SignupForm />
          </div>
        </div>
      </div>
    </Layout>
  )
}
