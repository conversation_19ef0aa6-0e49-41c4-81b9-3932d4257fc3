import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Layout } from '../components/layout/Layout'
import { LoginForm } from '../components/auth/LoginForm'
import { useAuth } from '../hooks/useAuth'

export const LoginPage = () => {
  const { user, loading } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!loading && user) {
      navigate('/dashboard')
    }
  }, [user, loading, navigate])

  if (loading) {
    return (
      <Layout showHeader={false}>
        <div className="auth-page">
          <div className="container">
            <p>Loading...</p>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout showHeader={false}>
      <div className="auth-page">
        <div className="container">
          <div className="auth-container">
            <LoginForm />
          </div>
        </div>
      </div>
    </Layout>
  )
}
