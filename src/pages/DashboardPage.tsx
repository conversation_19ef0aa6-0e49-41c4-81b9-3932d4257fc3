import { Layout } from '../components/layout/Layout'
import { ServicesList } from '../components/services/ServicesList'
import { LoginPage } from './LoginPage'
import { useAuth } from '../hooks/useAuth'

export const DashboardPage = () => {
  const { user, loading, isLoggedIn } = useAuth()

  if (loading) {
    return (
      <Layout>
        <div className="dashboard-page">
          <div className="container">
            <p>Loading...</p>
          </div>
        </div>
      </Layout>
    )
  }

  if (!isLoggedIn) {
    return <LoginPage />
  }

  return (
    <Layout>
      <div className="dashboard-page">
        <div className="container">
          <div className="dashboard-header">
            <h1>Dashboard</h1>
            <p>Welcome back, {user?.email}!</p>
          </div>

          <ServicesList />
        </div>
      </div>
    </Layout>
  )
}
