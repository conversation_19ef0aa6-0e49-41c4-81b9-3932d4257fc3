import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Layout } from '../components/layout/Layout'
import { ServicesList } from '../components/services/ServicesList'
import { useAuth } from '../hooks/useAuth'

export const DashboardPage = () => {
  const { user, loading } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login')
    }
  }, [user, loading, navigate])

  if (loading) {
    return (
      <Layout>
        <div className="dashboard-page">
          <div className="container">
            <p>Loading...</p>
          </div>
        </div>
      </Layout>
    )
  }

  if (!user) {
    return null // Will redirect to login
  }

  return (
    <Layout>
      <div className="dashboard-page">
        <div className="container">
          <div className="dashboard-header">
            <h1>Dashboard</h1>
            <p>Welcome back, {user.email}!</p>
          </div>
          
          <ServicesList />
        </div>
      </div>
    </Layout>
  )
}
